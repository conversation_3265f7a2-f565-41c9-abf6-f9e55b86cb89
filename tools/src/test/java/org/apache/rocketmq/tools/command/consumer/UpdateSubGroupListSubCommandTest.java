/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.rocketmq.tools.command.consumer;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.Options;
import org.apache.rocketmq.srvutil.ServerUtil;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class UpdateSubGroupListSubCommandTest {

    @Test
    public void testArguments() {
        UpdateSubGroupListSubCommand cmd = new UpdateSubGroupListSubCommand();
        Options options = ServerUtil.buildCommandlineOptions(new Options());

        String brokerAddress = "127.0.0.1:10911";
        String inputFileName = "groups.json";
        String[] args = new String[] {"-b " + brokerAddress, "-f " + inputFileName};
        final CommandLine commandLine =
            ServerUtil.parseCmdLine("mqadmin " + cmd.commandName(), args,
                cmd.buildCommandlineOptions(options), new DefaultParser());

        assertEquals(brokerAddress, commandLine.getOptionValue('b').trim());
        assertEquals(inputFileName, commandLine.getOptionValue('f').trim());
    }
}