#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
header:
  license:
    spdx-id: Apache-2.0
    copyright-owner: Apache Software Foundation

  paths-ignore:
    - '.gitignore'
    - '.travis.yml'
    - 'CONTRIBUTING.md'
    - 'LICENSE'
    - 'NOTICE'
    - '**/*.md'
    - 'BUILDING'
    - '.github/**'
    - '*/src/test/resources/certs/*'
    - 'src/test/**/*.log'
    - '*/src/test/resources/META-INF/service/*'
    - '*/src/main/resources/META-INF/service/*'
    - '*/src/test/resources/rmq-proxy-home/conf/rmq-proxy.json'
    - '*/src/test/resources/mockito-extensions/*'
    - '**/target/**'
    - '**/*.iml'
    - 'docs/**'
    - 'localbin/**'
    - 'distribution/LICENSE-BIN'
    - 'distribution/NOTICE-BIN'
    - 'distribution/conf/rmq-proxy.json'
    - '.bazelversion'
    - 'common/src/main/resources/META-INF/services/org.apache.rocketmq.logging.ch.qos.logback.classic.spi.Configurator'


  comment: on-failure