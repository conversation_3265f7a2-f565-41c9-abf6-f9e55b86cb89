/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.rocketmq.common.constant;

public class LoggerName {
    public static final String FILTERSRV_LOGGER_NAME = "RocketmqFiltersrv";
    public static final String NAMESRV_LOGGER_NAME = "RocketmqNamesrv";
    public static final String NAMESRV_CONSOLE_LOGGER_NAME = "RocketmqNamesrvConsole";
    public static final String CONTROLLER_LOGGER_NAME = "RocketmqController";
    public static final String CONTROLLER_CONSOLE_NAME = "RocketmqControllerConsole";
    public static final String NAMESRV_WATER_MARK_LOGGER_NAME = "RocketmqNamesrvWaterMark";
    public static final String BROKER_LOGGER_NAME = "RocketmqBroker";
    public static final String BROKER_CONSOLE_NAME = "RocketmqConsole";
    public static final String CLIENT_LOGGER_NAME = "RocketmqClient";
    public static final String ROCKETMQ_TRAFFIC_NAME = "RocketmqTraffic";
    public static final String ROCKETMQ_REMOTING_NAME = "RocketmqRemoting";
    public static final String TOOLS_LOGGER_NAME = "RocketmqTools";
    public static final String COMMON_LOGGER_NAME = "RocketmqCommon";
    public static final String STORE_LOGGER_NAME = "RocketmqStore";
    public static final String STORE_ERROR_LOGGER_NAME = "RocketmqStoreError";
    public static final String TRANSACTION_LOGGER_NAME = "RocketmqTransaction";
    public static final String REBALANCE_LOCK_LOGGER_NAME = "RocketmqRebalanceLock";
    public static final String ROCKETMQ_STATS_LOGGER_NAME = "RocketmqStats";
    public static final String DLQ_STATS_LOGGER_NAME = "RocketmqDLQStats";
    public static final String DLQ_LOGGER_NAME = "RocketmqDLQ";
    public static final String CONSUMER_STATS_LOGGER_NAME = "RocketmqConsumerStats";
    public static final String COMMERCIAL_LOGGER_NAME = "RocketmqCommercial";
    public static final String ACCOUNT_LOGGER_NAME = "RocketmqAccount";
    public static final String FLOW_CONTROL_LOGGER_NAME = "RocketmqFlowControl";
    public static final String ROCKETMQ_AUTHORIZE_LOGGER_NAME = "RocketmqAuthorize";
    public static final String DUPLICATION_LOGGER_NAME = "RocketmqDuplication";
    public static final String PROTECTION_LOGGER_NAME = "RocketmqProtection";
    public static final String WATER_MARK_LOGGER_NAME = "RocketmqWaterMark";
    public static final String FILTER_LOGGER_NAME = "RocketmqFilter";
    public static final String ROCKETMQ_POP_LOGGER_NAME = "RocketmqPop";
    public static final String FAILOVER_LOGGER_NAME = "RocketmqFailover";
    public static final String STDOUT_LOGGER_NAME = "STDOUT";
    public static final String PROXY_LOGGER_NAME = "RocketmqProxy";
    public static final String PROXY_WATER_MARK_LOGGER_NAME = "RocketmqProxyWatermark";
    public static final String ROCKETMQ_COLDCTR_LOGGER_NAME = "RocketmqColdCtr";
    public static final String ROCKSDB_LOGGER_NAME = "RocketmqRocksDB";

    public static final String ROCKETMQ_AUTH_AUDIT_LOGGER_NAME = "RocketmqAuthAudit";
}
