/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.rocketmq.common.utils;

import java.util.concurrent.ConcurrentHashMap;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ConcurrentHashMapUtilsTest {

    @Test
    public void computeIfAbsent() {
        ConcurrentHashMap<String, String> map = new ConcurrentHashMap<>();
        map.put("123", "1111");
        String value = ConcurrentHashMapUtils.computeIfAbsent(map, "123", k -> "234");
        assertEquals("1111", value);
        String value1 = ConcurrentHashMapUtils.computeIfAbsent(map, "1232", k -> "2342");
        assertEquals("2342", value1);
        String value2 = ConcurrentHashMapUtils.computeIfAbsent(map, "123", k -> "2342");
        assertEquals("1111", value2);
    }
}