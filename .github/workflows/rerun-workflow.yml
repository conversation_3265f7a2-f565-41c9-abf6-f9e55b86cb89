name: Rerun workflow
on:
  workflow_run:
    workflows: ["Build and Run Tests by <PERSON><PERSON>" , "Build and Run Tests by <PERSON><PERSON>"]
    types:
      - completed

permissions:
  actions: write

jobs:
  rerun:
    if: github.event.workflow_run.conclusion == 'failure' && fromJSON(github.event.workflow_run.run_attempt) < 3
    runs-on: ubuntu-latest
    steps:
      - name: rerun ${{  github.event.workflow_run.id }}
        env:
          GH_REPO:  ${{ github.repository }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh run watch ${{ github.event.workflow_run.id }} > /dev/null 2>&1
          gh run rerun ${{ github.event.workflow_run.id }} --failed