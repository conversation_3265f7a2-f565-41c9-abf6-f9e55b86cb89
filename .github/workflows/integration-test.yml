name: Run Integration Tests
on:
  pull_request:
    types: [opened, reopened, synchronize]
  push:
    branches: [master, develop]

jobs:
  it-test:
    name: "maven-compile (${{ matrix.os }}, JDK-${{ matrix.jdk }})"
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest]
        jdk: [8]
    steps:
      - name: <PERSON><PERSON> Maven Rep<PERSON>
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up JDK ${{ matrix.jdk }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ matrix.jdk }}
          distribution: "corretto"
          cache: "maven"

      - name: Run integration tests with Maven
        run: mvn clean verify -Pit-test -Pskip-unit-tests

      - name: Publish Test Report
        uses: mikepenz/action-junit-report@v3
        if: always()
        with:
          report_paths: 'test/target/failsafe-reports/TEST-*.xml'
          annotate_only: true
          include_passed: true
          detailed_summary: true
