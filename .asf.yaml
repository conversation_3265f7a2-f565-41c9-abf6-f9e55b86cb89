# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

github:
  description: "Apache RocketMQ is a cloud native messaging and streaming platform, making it simple to build event-driven applications."
  homepage: https://rocketmq.apache.org/
  labels:
    - messaging
    - streaming
    - eventing
    - cloud-native
    - rocketmq
    - java
    - hacktoberfest
  enabled_merge_buttons:
    # Enable squash button
    squash: true
    # Disable merge button
    merge: false
    # Disable rebase button
    rebase: false
  protected_branches:
    master: {}
    develop:
      required_pull_request_reviews:
        dismiss_stale_reviews: true
        require_code_owner_reviews: false
        required_approving_review_count: 1
      required_status_checks:
        contexts:
          - misspell-check
          - check-license
          - maven-compile (ubuntu-latest, JDK-8)
          - maven-compile (windows-latest, JDK-8)
          - maven-compile (macos-latest, JDK-8)
notifications:
  commits:      <EMAIL>
  issues:       <EMAIL>
  pullrequests: <EMAIL>
  jobs:         <EMAIL>
  discussions:  <EMAIL>
