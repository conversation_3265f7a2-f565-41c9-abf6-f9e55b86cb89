#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
load("//bazel:GenTestRules.bzl", "GenTestRules")

java_library(
    name = "client",
    srcs = glob(["src/main/java/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "//common",
        "//remoting",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:commons_validator_commons_validator",
        "@maven//:com_github_luben_zstd_jni",
        "@maven//:org_lz4_lz4_java",
        "@maven//:com_alibaba_fastjson",
        "@maven//:com_alibaba_fastjson2_fastjson2",
        "@maven//:io_netty_netty_all",
        "@maven//:io_opentracing_opentracing_api",
        "@maven//:commons_collections_commons_collections",
        "@maven//:io_github_aliyunmq_rocketmq_slf4j_api",
        "@maven//:io_github_aliyunmq_rocketmq_logback_classic",
        "@maven//:com_google_guava_guava",
        "@maven//:commons_codec_commons_codec",
        "@maven//:org_yaml_snakeyaml",
    ],
)

java_library(
    name = "tests",
    srcs = glob(["src/test/java/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        ":client",
        "//remoting",
        "//common",
        "//:test_deps",
        "@maven//:org_apache_commons_commons_lang3", 
        "@maven//:io_netty_netty_all",   
        "@maven//:io_opentracing_opentracing_api",        
        "@maven//:io_opentracing_opentracing_mock",   
        "@maven//:org_awaitility_awaitility",
        "@maven//:org_mockito_mockito_junit_jupiter",
        "@maven//:com_alibaba_fastjson2_fastjson2",
    ],
    resources = glob(["src/test/resources/certs/*.pem"]) + glob(["src/test/resources/certs/*.key"]) + glob(["src/test/resources/**/*.yml"])
)

GenTestRules(
    name = "GeneratedTestRules",
    test_files = glob(["src/test/java/**/*Test.java"]),
    deps = [
        ":tests",
    ],
    exclude_tests = [
        "src/test/java/org/apache/rocketmq/client/consumer/DefaultLitePullConsumerTest",
    ],
)
