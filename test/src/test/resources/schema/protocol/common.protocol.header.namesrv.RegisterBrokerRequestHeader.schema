/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field bodyCrc32 : private java.lang.Integer 0
Field brokerAddr : private java.lang.String null
Field brokerId : private java.lang.Long null
Field brokerName : private java.lang.String null
Field clusterName : private java.lang.String null
Field compressed : private boolean false
Field haServerAddr : private java.lang.String null
Method checkFields() : public throws (void)
Method getBodyCrc32() : public throws (java.lang.Integer)
Method getBrokerAddr() : public throws (java.lang.String)
Method getBrokerId() : public throws (java.lang.Long)
Method getBrokerName() : public throws (java.lang.String)
Method getClusterName() : public throws (java.lang.String)
Method getHaServerAddr() : public throws (java.lang.String)
Method isCompressed() : public throws (boolean)
Method setBodyCrc32(java.lang.Integer) : public throws (void)
Method setBrokerAddr(java.lang.String) : public throws (void)
Method setBrokerId(java.lang.Long) : public throws (void)
Method setBrokerName(java.lang.String) : public throws (void)
Method setClusterName(java.lang.String) : public throws (void)
Method setCompressed(boolean) : public throws (void)
Method setHaServerAddr(java.lang.String) : public throws (void)
