/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field beginTimestamp : private java.lang.Long null
Field endTimestamp : private java.lang.Long null
Field key : private java.lang.String null
Field maxNum : private java.lang.Integer null
Field topic : private java.lang.String null
Method checkFields() : public throws (void)
Method getBeginTimestamp() : public throws (java.lang.Long)
Method getEndTimestamp() : public throws (java.lang.Long)
Method getKey() : public throws (java.lang.String)
Method getMaxNum() : public throws (java.lang.Integer)
Method getTopic() : public throws (java.lang.String)
Method setBeginTimestamp(java.lang.Long) : public throws (void)
Method setEndTimestamp(java.lang.Long) : public throws (void)
Method setKey(java.lang.String) : public throws (void)
Method setMaxNum(java.lang.Integer) : public throws (void)
Method setTopic(java.lang.String) : public throws (void)
