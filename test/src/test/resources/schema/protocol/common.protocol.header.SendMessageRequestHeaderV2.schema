/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field a : private java.lang.String null
Field b : private java.lang.String null
Field c : private java.lang.String null
Field d : private java.lang.Integer null
Field e : private java.lang.Integer null
Field f : private java.lang.Integer null
Field g : private java.lang.Long null
Field h : private java.lang.Integer null
Field i : private java.lang.String null
Field j : private java.lang.Integer null
Field k : private boolean false
Field l : private java.lang.Integer null
Field m : private boolean false
Method checkFields() : public throws (void)
Method createSendMessageRequestHeaderV1(org.apache.rocketmq.remoting.protocol.header.SendMessageRequestHeaderV2) : public throws (org.apache.rocketmq.remoting.protocol.header.SendMessageRequestHeader)
Method createSendMessageRequestHeaderV2(org.apache.rocketmq.remoting.protocol.header.SendMessageRequestHeader) : public throws (org.apache.rocketmq.remoting.protocol.header.SendMessageRequestHeaderV2)
Method decode(java.util.HashMap) : public throws (void)
Method encode(io.netty.buffer.ByteBuf) : public throws (void)
Method getA() : public throws (java.lang.String)
Method getB() : public throws (java.lang.String)
Method getC() : public throws (java.lang.String)
Method getD() : public throws (java.lang.Integer)
Method getE() : public throws (java.lang.Integer)
Method getF() : public throws (java.lang.Integer)
Method getG() : public throws (java.lang.Long)
Method getH() : public throws (java.lang.Integer)
Method getI() : public throws (java.lang.String)
Method getJ() : public throws (java.lang.Integer)
Method getL() : public throws (java.lang.Integer)
Method isK() : public throws (boolean)
Method isM() : public throws (boolean)
Method setA(java.lang.String) : public throws (void)
Method setB(java.lang.String) : public throws (void)
Method setC(java.lang.String) : public throws (void)
Method setD(java.lang.Integer) : public throws (void)
Method setE(java.lang.Integer) : public throws (void)
Method setF(java.lang.Integer) : public throws (void)
Method setG(java.lang.Long) : public throws (void)
Method setH(java.lang.Integer) : public throws (void)
Method setI(java.lang.String) : public throws (void)
Method setJ(java.lang.Integer) : public throws (void)
Method setK(boolean) : public throws (void)
Method setL(java.lang.Integer) : public throws (void)
Method setM(boolean) : public throws (void)
