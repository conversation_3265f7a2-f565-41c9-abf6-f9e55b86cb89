/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field defaultTopic : private java.lang.String null
Field order : private java.lang.Boolean false
Field perm : private java.lang.Integer null
Field readQueueNums : private java.lang.Integer null
Field topic : private java.lang.String null
Field topicFilterType : private java.lang.String null
Field topicSysFlag : private java.lang.Integer null
Field writeQueueNums : private java.lang.Integer null
Method checkFields() : public throws (void)
Method getDefaultTopic() : public throws (java.lang.String)
Method getOrder() : public throws (java.lang.Boolean)
Method getPerm() : public throws (java.lang.Integer)
Method getReadQueueNums() : public throws (java.lang.Integer)
Method getTopic() : public throws (java.lang.String)
Method getTopicFilterType() : public throws (java.lang.String)
Method getTopicFilterTypeEnum() : public throws (org.apache.rocketmq.common.TopicFilterType)
Method getTopicSysFlag() : public throws (java.lang.Integer)
Method getWriteQueueNums() : public throws (java.lang.Integer)
Method setDefaultTopic(java.lang.String) : public throws (void)
Method setOrder(java.lang.Boolean) : public throws (void)
Method setPerm(java.lang.Integer) : public throws (void)
Method setReadQueueNums(java.lang.Integer) : public throws (void)
Method setTopic(java.lang.String) : public throws (void)
Method setTopicFilterType(java.lang.String) : public throws (void)
Method setTopicSysFlag(java.lang.Integer) : public throws (void)
Method setWriteQueueNums(java.lang.Integer) : public throws (void)
