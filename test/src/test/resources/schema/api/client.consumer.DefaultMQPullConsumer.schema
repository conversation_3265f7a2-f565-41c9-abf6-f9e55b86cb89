/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field SEND_MESSAGE_WITH_VIP_CHANNEL_PROPERTY : public java.lang.String com.rocketmq.sendMessageWithVIPChannel
Field accessChannel : protected org.apache.rocketmq.client.AccessChannel LOCAL
Field allocateMessageQueueStrategy : private org.apache.rocketmq.client.consumer.AllocateMessageQueueStrategy null
Field brokerSuspendMaxTimeMillis : private long 20000
Field clientCallbackExecutorThreads : private int null
Field clientIP : private java.lang.String null
Field consumerGroup : private java.lang.String DEFAULT_CONSUMER
Field consumerPullTimeoutMillis : private long 10000
Field consumerTimeoutMillisWhenSuspend : private long 30000
Field defaultMQPullConsumerImpl : protected org.apache.rocketmq.client.impl.consumer.DefaultMQPullConsumerImpl null
Field enableStreamRequestType : protected boolean true
Field heartbeatBrokerInterval : private int 30000
Field instanceName : private java.lang.String DEFAULT
Field language : private org.apache.rocketmq.remoting.protocol.LanguageCode JAVA
Field maxReconsumeTimes : private int 16
Field messageModel : private org.apache.rocketmq.remoting.protocol.heartbeat.MessageModel CLUSTERING
Field messageQueueListener : private org.apache.rocketmq.client.consumer.MessageQueueListener null
Field mqClientApiTimeout : private int 3000
Field namespace : protected java.lang.String null
Field namespaceInitialized : private boolean false
Field namesrvAddr : private java.lang.String null
Field offsetStore : private org.apache.rocketmq.client.consumer.store.OffsetStore null
Field persistConsumerOffsetInterval : private int 5000
Field pollNameServerInterval : private int 30000
Field pullTimeDelayMillsWhenException : private long 1000
Field registerTopics : private java.util.Set null
Field unitMode : private boolean false
Field unitName : private java.lang.String null
Field useTLS : private boolean false
Field vipChannelEnabled : private boolean false
Method buildMQClientId() : public throws (java.lang.String)
Method changeInstanceNameToPID() : public throws (void)
Method cloneClientConfig() : public throws (org.apache.rocketmq.client.ClientConfig)
Method createTopic(int,int,java.lang.String,java.lang.String) : public throws (void)
Method createTopic(int,java.lang.String,java.lang.String) : public throws (void)
Method earliestMsgStoreTime(org.apache.rocketmq.common.message.MessageQueue) : public throws (long)
Method fetchConsumeOffset(boolean,org.apache.rocketmq.common.message.MessageQueue) : public throws (long)
Method fetchMessageQueuesInBalance(java.lang.String) : public throws (java.util.Set)
Method fetchSubscribeMessageQueues(java.lang.String) : public throws (java.util.Set)
Method getAccessChannel() : public throws (org.apache.rocketmq.client.AccessChannel)
Method getAllocateMessageQueueStrategy() : public throws (org.apache.rocketmq.client.consumer.AllocateMessageQueueStrategy)
Method getBrokerSuspendMaxTimeMillis() : public throws (long)
Method getClientCallbackExecutorThreads() : public throws (int)
Method getClientIP() : public throws (java.lang.String)
Method getConsumerGroup() : public throws (java.lang.String)
Method getConsumerPullTimeoutMillis() : public throws (long)
Method getConsumerTimeoutMillisWhenSuspend() : public throws (long)
Method getDefaultMQPullConsumerImpl() : public throws (org.apache.rocketmq.client.impl.consumer.DefaultMQPullConsumerImpl)
Method getHeartbeatBrokerInterval() : public throws (int)
Method getInstanceName() : public throws (java.lang.String)
Method getLanguage() : public throws (org.apache.rocketmq.remoting.protocol.LanguageCode)
Method getMaxReconsumeTimes() : public throws (int)
Method getMessageModel() : public throws (org.apache.rocketmq.remoting.protocol.heartbeat.MessageModel)
Method getMessageQueueListener() : public throws (org.apache.rocketmq.client.consumer.MessageQueueListener)
Method getMqClientApiTimeout() : public throws (int)
Method getNamespace() : public throws (java.lang.String)
Method getNamesrvAddr() : public throws (java.lang.String)
Method getOffsetStore() : public throws (org.apache.rocketmq.client.consumer.store.OffsetStore)
Method getPersistConsumerOffsetInterval() : public throws (int)
Method getPollNameServerInterval() : public throws (int)
Method getPullTimeDelayMillsWhenException() : public throws (long)
Method getRegisterTopics() : public throws (java.util.Set)
Method getUnitName() : public throws (java.lang.String)
Method isEnableStreamRequestType() : public throws (boolean)
Method isUnitMode() : public throws (boolean)
Method isUseTLS() : public throws (boolean)
Method isVipChannelEnabled() : public throws (boolean)
Method maxOffset(org.apache.rocketmq.common.message.MessageQueue) : public throws (long)
Method minOffset(org.apache.rocketmq.common.message.MessageQueue) : public throws (long)
Method pull(int,java.lang.String,long,long,org.apache.rocketmq.client.consumer.PullCallback,org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method pull(int,java.lang.String,long,long,org.apache.rocketmq.common.message.MessageQueue) : public throws (org.apache.rocketmq.client.consumer.PullResult)
Method pull(int,java.lang.String,long,org.apache.rocketmq.client.consumer.PullCallback,org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method pull(int,java.lang.String,long,org.apache.rocketmq.common.message.MessageQueue) : public throws (org.apache.rocketmq.client.consumer.PullResult)
Method pull(int,long,long,org.apache.rocketmq.client.consumer.MessageSelector,org.apache.rocketmq.client.consumer.PullCallback,org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method pull(int,long,long,org.apache.rocketmq.client.consumer.MessageSelector,org.apache.rocketmq.common.message.MessageQueue) : public throws (org.apache.rocketmq.client.consumer.PullResult)
Method pull(int,long,org.apache.rocketmq.client.consumer.MessageSelector,org.apache.rocketmq.client.consumer.PullCallback,org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method pull(int,long,org.apache.rocketmq.client.consumer.MessageSelector,org.apache.rocketmq.common.message.MessageQueue) : public throws (org.apache.rocketmq.client.consumer.PullResult)
Method pullBlockIfNotFound(int,java.lang.String,long,org.apache.rocketmq.client.consumer.PullCallback,org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method pullBlockIfNotFound(int,java.lang.String,long,org.apache.rocketmq.common.message.MessageQueue) : public throws (org.apache.rocketmq.client.consumer.PullResult)
Method queryMessage(int,java.lang.String,java.lang.String,long,long) : public throws (org.apache.rocketmq.client.QueryResult)
Method queueWithNamespace(org.apache.rocketmq.common.message.MessageQueue) : public throws (org.apache.rocketmq.common.message.MessageQueue)
Method queuesWithNamespace(java.util.Collection) : public throws (java.util.Collection)
Method registerMessageQueueListener(java.lang.String,org.apache.rocketmq.client.consumer.MessageQueueListener) : public throws (void)
Method resetClientConfig(org.apache.rocketmq.client.ClientConfig) : public throws (void)
Method searchOffset(long,org.apache.rocketmq.common.message.MessageQueue) : public throws (long)
Method sendMessageBack(int,java.lang.String,java.lang.String,org.apache.rocketmq.common.message.MessageExt) : public throws (void)
Method sendMessageBack(int,java.lang.String,org.apache.rocketmq.common.message.MessageExt) : public throws (void)
Method sendMessageBack(int,org.apache.rocketmq.common.message.MessageExt) : public throws (void)
Method setAccessChannel(org.apache.rocketmq.client.AccessChannel) : public throws (void)
Method setAllocateMessageQueueStrategy(org.apache.rocketmq.client.consumer.AllocateMessageQueueStrategy) : public throws (void)
Method setBrokerSuspendMaxTimeMillis(long) : public throws (void)
Method setClientCallbackExecutorThreads(int) : public throws (void)
Method setClientIP(java.lang.String) : public throws (void)
Method setConsumerGroup(java.lang.String) : public throws (void)
Method setConsumerPullTimeoutMillis(long) : public throws (void)
Method setConsumerTimeoutMillisWhenSuspend(long) : public throws (void)
Method setEnableStreamRequestType(boolean) : public throws (void)
Method setHeartbeatBrokerInterval(int) : public throws (void)
Method setInstanceName(java.lang.String) : public throws (void)
Method setLanguage(org.apache.rocketmq.remoting.protocol.LanguageCode) : public throws (void)
Method setMaxReconsumeTimes(int) : public throws (void)
Method setMessageModel(org.apache.rocketmq.remoting.protocol.heartbeat.MessageModel) : public throws (void)
Method setMessageQueueListener(org.apache.rocketmq.client.consumer.MessageQueueListener) : public throws (void)
Method setMqClientApiTimeout(int) : public throws (void)
Method setNamespace(java.lang.String) : public throws (void)
Method setNamesrvAddr(java.lang.String) : public throws (void)
Method setOffsetStore(org.apache.rocketmq.client.consumer.store.OffsetStore) : public throws (void)
Method setPersistConsumerOffsetInterval(int) : public throws (void)
Method setPollNameServerInterval(int) : public throws (void)
Method setPullTimeDelayMillsWhenException(long) : public throws (void)
Method setRegisterTopics(java.util.Set) : public throws (void)
Method setUnitMode(boolean) : public throws (void)
Method setUnitName(java.lang.String) : public throws (void)
Method setUseTLS(boolean) : public throws (void)
Method setVipChannelEnabled(boolean) : public throws (void)
Method shutdown() : public throws (void)
Method start() : public throws (void)
Method toString() : public throws (java.lang.String)
Method updateConsumeOffset(long,org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method viewMessage(java.lang.String) : public throws (org.apache.rocketmq.common.message.MessageExt)
Method viewMessage(java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.common.message.MessageExt)
Method withNamespace(java.lang.String) : public throws (java.lang.String)
Method withNamespace(java.util.Set) : public throws (java.util.Set)
Method withoutNamespace(java.lang.String) : public throws (java.lang.String)
Method withoutNamespace(java.util.Set) : public throws (java.util.Set)
