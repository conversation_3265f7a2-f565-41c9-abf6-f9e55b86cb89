/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field SEND_MESSAGE_WITH_VIP_CHANNEL_PROPERTY : public java.lang.String com.rocketmq.sendMessageWithVIPChannel
Field accessChannel : protected org.apache.rocketmq.client.AccessChannel LOCAL
Field adminExtGroup : private java.lang.String admin_ext_group
Field clientCallbackExecutorThreads : private int null
Field clientIP : private java.lang.String null
Field createTopicKey : private java.lang.String TBW102
Field defaultMQAdminExtImpl : private org.apache.rocketmq.tools.admin.DefaultMQAdminExtImpl null
Field enableStreamRequestType : protected boolean false
Field heartbeatBrokerInterval : private int 30000
Field instanceName : private java.lang.String DEFAULT
Field language : private org.apache.rocketmq.remoting.protocol.LanguageCode JAVA
Field mqClientApiTimeout : private int 3000
Field namespace : protected java.lang.String null
Field namespaceInitialized : private boolean false
Field namesrvAddr : private java.lang.String null
Field persistConsumerOffsetInterval : private int 5000
Field pollNameServerInterval : private int 30000
Field pullTimeDelayMillsWhenException : private long 1000
Field timeoutMillis : private long 5000
Field unitMode : private boolean false
Field unitName : private java.lang.String null
Field useTLS : private boolean false
Field vipChannelEnabled : private boolean false
Method addWritePermOfBroker(java.lang.String,java.lang.String) : public throws (int)
Method buildMQClientId() : public throws (java.lang.String)
Method changeInstanceNameToPID() : public throws (void)
Method cleanExpiredConsumerQueue(java.lang.String) : public throws (boolean)
Method cleanExpiredConsumerQueueByAddr(java.lang.String) : public throws (boolean)
Method cleanUnusedTopic(java.lang.String) : public throws (boolean)
Method cleanUnusedTopicByAddr(java.lang.String) : public throws (boolean)
Method cloneClientConfig() : public throws (org.apache.rocketmq.client.ClientConfig)
Method cloneGroupOffset(boolean,java.lang.String,java.lang.String,java.lang.String) : public throws (void)
Method consumeMessageDirectly(java.lang.String,java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.ConsumeMessageDirectlyResult)
Method consumeMessageDirectly(java.lang.String,java.lang.String,java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.ConsumeMessageDirectlyResult)
Method createAndUpdateKvConfig(java.lang.String,java.lang.String,java.lang.String) : public throws (void)
Method createAndUpdatePlainAccessConfig(java.lang.String,org.apache.rocketmq.auth.migration.plain.PlainAccessConfig) : public throws (void)
Method createAndUpdateSubscriptionGroupConfig(java.lang.String,org.apache.rocketmq.remoting.protocol.subscription.SubscriptionGroupConfig) : public throws (void)
Method createAndUpdateTopicConfig(java.lang.String,org.apache.rocketmq.common.TopicConfig) : public throws (void)
Method createOrUpdateOrderConf(boolean,java.lang.String,java.lang.String) : public throws (void)
Method createTopic(int,int,java.lang.String,java.lang.String) : public throws (void)
Method createTopic(int,java.lang.String,java.lang.String) : public throws (void)
Method deleteExpiredCommitLog(java.lang.String) : public throws (boolean)
Method deleteExpiredCommitLogByAddr(java.lang.String) : public throws (boolean)
Method deleteKvConfig(java.lang.String,java.lang.String) : public throws (void)
Method deletePlainAccessConfig(java.lang.String,java.lang.String) : public throws (void)
Method deleteSubscriptionGroup(boolean,java.lang.String,java.lang.String) : public throws (void)
Method deleteSubscriptionGroup(java.lang.String,java.lang.String) : public throws (void)
Method deleteTopicInBroker(java.lang.String,java.util.Set) : public throws (void)
Method deleteTopicInNameServer(java.lang.String,java.lang.String,java.util.Set) : public throws (void)
Method earliestMsgStoreTime(org.apache.rocketmq.common.message.MessageQueue) : public throws (long)
Method examineBrokerClusterAclConfig(java.lang.String) : public throws (org.apache.rocketmq.auth.migration.plain.AclConfig)
Method examineBrokerClusterAclVersionInfo(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.ClusterAclVersionInfo)
Method examineBrokerClusterInfo() : public throws (org.apache.rocketmq.remoting.protocol.body.ClusterInfo)
Method examineConsumeStats(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.admin.ConsumeStats)
Method examineConsumeStats(java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.admin.ConsumeStats)
Method examineConsumerConnectionInfo(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.ConsumerConnection)
Method examineConsumerConnectionInfo(java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.ConsumerConnection)
Method examineProducerConnectionInfo(java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.ProducerConnection)
Method examineSubscriptionGroupConfig(java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.subscription.SubscriptionGroupConfig)
Method examineTopicConfig(java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.common.TopicConfig)
Method examineTopicRouteInfo(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.route.TopicRouteData)
Method examineTopicStats(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.admin.TopicStatsTable)
Method fetchAllTopicList() : public throws (org.apache.rocketmq.remoting.protocol.body.TopicList)
Method fetchBrokerRuntimeStats(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.KVTable)
Method fetchConsumeStatsInBroker(boolean,java.lang.String,long) : public throws (org.apache.rocketmq.remoting.protocol.body.ConsumeStatsList)
Method fetchTopicsByCLuster(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.TopicList)
Method getAccessChannel() : public throws (org.apache.rocketmq.client.AccessChannel)
Method getAdminExtGroup() : public throws (java.lang.String)
Method getAllProducerInfo(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.ProducerTableInfo)
Method getAllSubscriptionGroup(java.lang.String,long) : public throws (org.apache.rocketmq.remoting.protocol.body.SubscriptionGroupWrapper)
Method getAllTopicConfig(java.lang.String,long) : public throws (org.apache.rocketmq.remoting.protocol.body.TopicConfigSerializeWrapper)
Method getBrokerConfig(java.lang.String) : public throws (java.util.Properties)
Method getClientCallbackExecutorThreads() : public throws (int)
Method getClientIP() : public throws (java.lang.String)
Method getClusterList(java.lang.String) : public throws (java.util.Set)
Method getConsumeStatus(java.lang.String,java.lang.String,java.lang.String) : public throws (java.util.Map)
Method getConsumerRunningInfo(boolean,java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.ConsumerRunningInfo)
Method getCreateTopicKey() : public throws (java.lang.String)
Method getHeartbeatBrokerInterval() : public throws (int)
Method getInstanceName() : public throws (java.lang.String)
Method getKVConfig(java.lang.String,java.lang.String) : public throws (java.lang.String)
Method getKVListByNamespace(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.KVTable)
Method getLanguage() : public throws (org.apache.rocketmq.remoting.protocol.LanguageCode)
Method getMqClientApiTimeout() : public throws (int)
Method getNameServerAddressList() : public throws (java.util.List)
Method getNameServerConfig(java.util.List) : public throws (java.util.Map)
Method getNamespace() : public throws (java.lang.String)
Method getNamesrvAddr() : public throws (java.lang.String)
Method getPersistConsumerOffsetInterval() : public throws (int)
Method getPollNameServerInterval() : public throws (int)
Method getPullTimeDelayMillsWhenException() : public throws (long)
Method getTopicClusterList(java.lang.String) : public throws (java.util.Set)
Method getUnitName() : public throws (java.lang.String)
Method getUserSubscriptionGroup(java.lang.String,long) : public throws (org.apache.rocketmq.remoting.protocol.body.SubscriptionGroupWrapper)
Method getUserTopicConfig(boolean,java.lang.String,long) : public throws (org.apache.rocketmq.remoting.protocol.body.TopicConfigSerializeWrapper)
Method isEnableStreamRequestType() : public throws (boolean)
Method isUnitMode() : public throws (boolean)
Method isUseTLS() : public throws (boolean)
Method isVipChannelEnabled() : public throws (boolean)
Method maxOffset(org.apache.rocketmq.common.message.MessageQueue) : public throws (long)
Method messageTrackDetail(org.apache.rocketmq.common.message.MessageExt) : public throws (java.util.List)
Method minOffset(org.apache.rocketmq.common.message.MessageQueue) : public throws (long)
Method putKVConfig(java.lang.String,java.lang.String,java.lang.String) : public throws (void)
Method queryConsumeQueue(int,int,java.lang.String,java.lang.String,java.lang.String,long) : public throws (org.apache.rocketmq.remoting.protocol.body.QueryConsumeQueueResponseBody)
Method queryConsumeTimeSpan(java.lang.String,java.lang.String) : public throws (java.util.List)
Method queryMessage(int,java.lang.String,java.lang.String,long,long) : public throws (org.apache.rocketmq.client.QueryResult)
Method queryMessageByUniqKey(int,java.lang.String,java.lang.String,long,long) : public throws (org.apache.rocketmq.client.QueryResult)
Method queryTopicConsumeByWho(java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.GroupList)
Method queueWithNamespace(org.apache.rocketmq.common.message.MessageQueue) : public throws (org.apache.rocketmq.common.message.MessageQueue)
Method queuesWithNamespace(java.util.Collection) : public throws (java.util.Collection)
Method resetClientConfig(org.apache.rocketmq.client.ClientConfig) : public throws (void)
Method resetOffsetByTimestamp(boolean,boolean,java.lang.String,java.lang.String,long) : public throws (java.util.Map)
Method resetOffsetByTimestamp(boolean,java.lang.String,java.lang.String,long) : public throws (java.util.Map)
Method resetOffsetByTimestampOld(boolean,java.lang.String,java.lang.String,long) : public throws (java.util.List)
Method resetOffsetNew(java.lang.String,java.lang.String,long) : public throws (void)
Method resumeCheckHalfMessage(java.lang.String) : public throws (boolean)
Method resumeCheckHalfMessage(java.lang.String,java.lang.String) : public throws (boolean)
Method searchOffset(long,org.apache.rocketmq.common.message.MessageQueue) : public throws (long)
Method setAccessChannel(org.apache.rocketmq.client.AccessChannel) : public throws (void)
Method setAdminExtGroup(java.lang.String) : public throws (void)
Method setClientCallbackExecutorThreads(int) : public throws (void)
Method setClientIP(java.lang.String) : public throws (void)
Method setCreateTopicKey(java.lang.String) : public throws (void)
Method setEnableStreamRequestType(boolean) : public throws (void)
Method setHeartbeatBrokerInterval(int) : public throws (void)
Method setInstanceName(java.lang.String) : public throws (void)
Method setLanguage(org.apache.rocketmq.remoting.protocol.LanguageCode) : public throws (void)
Method setMqClientApiTimeout(int) : public throws (void)
Method setNamespace(java.lang.String) : public throws (void)
Method setNamesrvAddr(java.lang.String) : public throws (void)
Method setPersistConsumerOffsetInterval(int) : public throws (void)
Method setPollNameServerInterval(int) : public throws (void)
Method setPullTimeDelayMillsWhenException(long) : public throws (void)
Method setUnitMode(boolean) : public throws (void)
Method setUnitName(java.lang.String) : public throws (void)
Method setUseTLS(boolean) : public throws (void)
Method setVipChannelEnabled(boolean) : public throws (void)
Method shutdown() : public throws (void)
Method start() : public throws (void)
Method toString() : public throws (java.lang.String)
Method updateBrokerConfig(java.lang.String,java.util.Properties) : public throws (void)
Method updateConsumeOffset(java.lang.String,java.lang.String,long,org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method updateGlobalWhiteAddrConfig(java.lang.String,java.lang.String) : public throws (void)
Method updateGlobalWhiteAddrConfig(java.lang.String,java.lang.String,java.lang.String) : public throws (void)
Method updateNameServerConfig(java.util.List,java.util.Properties) : public throws (void)
Method viewBrokerStatsData(java.lang.String,java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.remoting.protocol.body.BrokerStatsData)
Method viewMessage(java.lang.String) : public throws (org.apache.rocketmq.common.message.MessageExt)
Method viewMessage(java.lang.String,java.lang.String) : public throws (org.apache.rocketmq.common.message.MessageExt)
Method wipeWritePermOfBroker(java.lang.String,java.lang.String) : public throws (int)
Method withNamespace(java.lang.String) : public throws (java.lang.String)
Method withNamespace(java.util.Set) : public throws (java.util.Set)
Method withoutNamespace(java.lang.String) : public throws (java.lang.String)
Method withoutNamespace(java.util.Set) : public throws (java.util.Set)
