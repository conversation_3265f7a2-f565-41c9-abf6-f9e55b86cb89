/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field maxOffset : private long 0
Field minOffset : private long 0
Field msgFoundList : private java.util.List null
Field nextBeginOffset : private long 0
Field pullStatus : private org.apache.rocketmq.client.consumer.PullStatus FOUND
Method getMaxOffset() : public throws (long)
Method getMinOffset() : public throws (long)
Method getMsgFoundList() : public throws (java.util.List)
Method getNextBeginOffset() : public throws (long)
Method getPullStatus() : public throws (org.apache.rocketmq.client.consumer.PullStatus)
Method setMsgFoundList(java.util.List) : public throws (void)
Method toString() : public throws (java.lang.String)
