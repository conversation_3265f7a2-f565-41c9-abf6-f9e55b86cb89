/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field consumerGroup : private java.lang.String null
Field mq : private org.apache.rocketmq.common.message.MessageQueue null
Field mqTraceContext : private java.lang.Object null
Field msgList : private java.util.List null
Field namespace : private java.lang.String null
Field props : private java.util.Map null
Field status : private java.lang.String null
Field success : private boolean false
Method getConsumerGroup() : public throws (java.lang.String)
Method getMq() : public throws (org.apache.rocketmq.common.message.MessageQueue)
Method getMqTraceContext() : public throws (java.lang.Object)
Method getMsgList() : public throws (java.util.List)
Method getNamespace() : public throws (java.lang.String)
Method getProps() : public throws (java.util.Map)
Method getStatus() : public throws (java.lang.String)
Method isSuccess() : public throws (boolean)
Method setConsumerGroup(java.lang.String) : public throws (void)
Method setMq(org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method setMqTraceContext(java.lang.Object) : public throws (void)
Method setMsgList(java.util.List) : public throws (void)
Method setNamespace(java.lang.String) : public throws (void)
Method setProps(java.util.Map) : public throws (void)
Method setStatus(java.lang.String) : public throws (void)
Method setSuccess(boolean) : public throws (void)
