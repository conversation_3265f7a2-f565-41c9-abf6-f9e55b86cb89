/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field brokerAddr : private java.lang.String null
Field fromTransactionCheck : private boolean false
Field message : private org.apache.rocketmq.common.message.Message null
Field msgId : private java.lang.String null
Field producerGroup : private java.lang.String null
Field transactionId : private java.lang.String null
Field transactionState : private org.apache.rocketmq.client.producer.LocalTransactionState null
Method getBrokerAddr() : public throws (java.lang.String)
Method getMessage() : public throws (org.apache.rocketmq.common.message.Message)
Method getMsgId() : public throws (java.lang.String)
Method getProducerGroup() : public throws (java.lang.String)
Method getTransactionId() : public throws (java.lang.String)
Method getTransactionState() : public throws (org.apache.rocketmq.client.producer.LocalTransactionState)
Method isFromTransactionCheck() : public throws (boolean)
Method setBrokerAddr(java.lang.String) : public throws (void)
Method setFromTransactionCheck(boolean) : public throws (void)
Method setMessage(org.apache.rocketmq.common.message.Message) : public throws (void)
Method setMsgId(java.lang.String) : public throws (void)
Method setProducerGroup(java.lang.String) : public throws (void)
Method setTransactionId(java.lang.String) : public throws (void)
Method setTransactionState(org.apache.rocketmq.client.producer.LocalTransactionState) : public throws (void)
