/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field MIN_AUTOCOMMIT_INTERVAL_MILLIS : private long 1000
Field SEND_MESSAGE_WITH_VIP_CHANNEL_PROPERTY : public java.lang.String com.rocketmq.sendMessageWithVIPChannel
Field accessChannel : protected org.apache.rocketmq.client.AccessChannel LOCAL
Field allocateMessageQueueStrategy : private org.apache.rocketmq.client.consumer.AllocateMessageQueueStrategy null
Field autoCommit : private boolean true
Field autoCommitIntervalMillis : private long 5000
Field brokerSuspendMaxTimeMillis : private long 20000
Field clientCallbackExecutorThreads : private int null
Field clientIP : private java.lang.String null
Field consumeFromWhere : private org.apache.rocketmq.common.consumer.ConsumeFromWhere CONSUME_FROM_LAST_OFFSET
Field consumeMaxSpan : private int 2000
Field consumeTimestamp : private java.lang.String null
Field consumerGroup : private java.lang.String DEFAULT_CONSUMER
Field consumerPullTimeoutMillis : private long 10000
Field consumerTimeoutMillisWhenSuspend : private long 30000
Field customizedTraceTopic : private java.lang.String null
Field defaultLitePullConsumerImpl : private org.apache.rocketmq.client.impl.consumer.DefaultLitePullConsumerImpl null
Field enableMsgTrace : private boolean false
Field enableStreamRequestType : protected boolean true
Field heartbeatBrokerInterval : private int 30000
Field instanceName : private java.lang.String DEFAULT
Field language : private org.apache.rocketmq.remoting.protocol.LanguageCode JAVA
Field log : private org.apache.rocketmq.logging.InternalLogger null
Field messageModel : private org.apache.rocketmq.remoting.protocol.heartbeat.MessageModel CLUSTERING
Field messageQueueListener : private org.apache.rocketmq.client.consumer.MessageQueueListener null
Field mqClientApiTimeout : private int 3000
Field namespace : protected java.lang.String null
Field namespaceInitialized : private boolean false
Field namesrvAddr : private java.lang.String null
Field offsetStore : private org.apache.rocketmq.client.consumer.store.OffsetStore null
Field persistConsumerOffsetInterval : private int 5000
Field pollNameServerInterval : private int 30000
Field pollTimeoutMillis : private long 5000
Field pullBatchSize : private int 10
Field pullThreadNums : private int 20
Field pullThresholdForAll : private long 10000
Field pullThresholdForQueue : private int 1000
Field pullThresholdSizeForQueue : private int 100
Field pullTimeDelayMillsWhenException : private long 1000
Field topicMetadataCheckIntervalMillis : private long 30000
Field traceDispatcher : private org.apache.rocketmq.client.trace.TraceDispatcher null
Field unitMode : private boolean false
Field unitName : private java.lang.String null
Field useTLS : private boolean false
Field vipChannelEnabled : private boolean false
Method assign(java.util.Collection) : public throws (void)
Method buildMQClientId() : public throws (java.lang.String)
Method changeInstanceNameToPID() : public throws (void)
Method cloneClientConfig() : public throws (org.apache.rocketmq.client.ClientConfig)
Method commit(boolean,java.util.Set) : public throws (void)
Method commitSync() : public throws (void)
Method committed(org.apache.rocketmq.common.message.MessageQueue) : public throws (java.lang.Long)
Method fetchMessageQueues(java.lang.String) : public throws (java.util.Collection)
Method getAccessChannel() : public throws (org.apache.rocketmq.client.AccessChannel)
Method getAllocateMessageQueueStrategy() : public throws (org.apache.rocketmq.client.consumer.AllocateMessageQueueStrategy)
Method getAutoCommitIntervalMillis() : public throws (long)
Method getBrokerSuspendMaxTimeMillis() : public throws (long)
Method getClientCallbackExecutorThreads() : public throws (int)
Method getClientIP() : public throws (java.lang.String)
Method getConsumeFromWhere() : public throws (org.apache.rocketmq.common.consumer.ConsumeFromWhere)
Method getConsumeMaxSpan() : public throws (int)
Method getConsumeTimestamp() : public throws (java.lang.String)
Method getConsumerGroup() : public throws (java.lang.String)
Method getConsumerPullTimeoutMillis() : public throws (long)
Method getConsumerTimeoutMillisWhenSuspend() : public throws (long)
Method getCustomizedTraceTopic() : public throws (java.lang.String)
Method getDefaultBrokerId() : public throws (long)
Method getHeartbeatBrokerInterval() : public throws (int)
Method getInstanceName() : public throws (java.lang.String)
Method getLanguage() : public throws (org.apache.rocketmq.remoting.protocol.LanguageCode)
Method getMessageModel() : public throws (org.apache.rocketmq.remoting.protocol.heartbeat.MessageModel)
Method getMessageQueueListener() : public throws (org.apache.rocketmq.client.consumer.MessageQueueListener)
Method getMqClientApiTimeout() : public throws (int)
Method getNamespace() : public throws (java.lang.String)
Method getNamesrvAddr() : public throws (java.lang.String)
Method getOffsetStore() : public throws (org.apache.rocketmq.client.consumer.store.OffsetStore)
Method getPersistConsumerOffsetInterval() : public throws (int)
Method getPollNameServerInterval() : public throws (int)
Method getPollTimeoutMillis() : public throws (long)
Method getPullBatchSize() : public throws (int)
Method getPullThreadNums() : public throws (int)
Method getPullThresholdForAll() : public throws (long)
Method getPullThresholdForQueue() : public throws (int)
Method getPullThresholdSizeForQueue() : public throws (int)
Method getPullTimeDelayMillsWhenException() : public throws (long)
Method getTopicMetadataCheckIntervalMillis() : public throws (long)
Method getTraceDispatcher() : public throws (org.apache.rocketmq.client.trace.TraceDispatcher)
Method getUnitName() : public throws (java.lang.String)
Method isAutoCommit() : public throws (boolean)
Method isConnectBrokerByUser() : public throws (boolean)
Method isEnableMsgTrace() : public throws (boolean)
Method isEnableStreamRequestType() : public throws (boolean)
Method isRunning() : public throws (boolean)
Method isUnitMode() : public throws (boolean)
Method isUseTLS() : public throws (boolean)
Method isVipChannelEnabled() : public throws (boolean)
Method offsetForTimestamp(java.lang.Long,org.apache.rocketmq.common.message.MessageQueue) : public throws (java.lang.Long)
Method pause(java.util.Collection) : public throws (void)
Method poll() : public throws (java.util.List)
Method poll(long) : public throws (java.util.List)
Method queueWithNamespace(org.apache.rocketmq.common.message.MessageQueue) : public throws (org.apache.rocketmq.common.message.MessageQueue)
Method queuesWithNamespace(java.util.Collection) : public throws (java.util.Collection)
Method registerTopicMessageQueueChangeListener(java.lang.String,org.apache.rocketmq.client.consumer.TopicMessageQueueChangeListener) : public throws (void)
Method resetClientConfig(org.apache.rocketmq.client.ClientConfig) : public throws (void)
Method resume(java.util.Collection) : public throws (void)
Method seek(long,org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method seekToBegin(org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method seekToEnd(org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method setAccessChannel(org.apache.rocketmq.client.AccessChannel) : public throws (void)
Method setAllocateMessageQueueStrategy(org.apache.rocketmq.client.consumer.AllocateMessageQueueStrategy) : public throws (void)
Method setAutoCommit(boolean) : public throws (void)
Method setAutoCommitIntervalMillis(long) : public throws (void)
Method setClientCallbackExecutorThreads(int) : public throws (void)
Method setClientIP(java.lang.String) : public throws (void)
Method setConnectBrokerByUser(boolean) : public throws (void)
Method setConsumeFromWhere(org.apache.rocketmq.common.consumer.ConsumeFromWhere) : public throws (void)
Method setConsumeMaxSpan(int) : public throws (void)
Method setConsumeTimestamp(java.lang.String) : public throws (void)
Method setConsumerGroup(java.lang.String) : public throws (void)
Method setConsumerPullTimeoutMillis(long) : public throws (void)
Method setConsumerTimeoutMillisWhenSuspend(long) : public throws (void)
Method setCustomizedTraceTopic(java.lang.String) : public throws (void)
Method setDefaultBrokerId(long) : public throws (void)
Method setEnableMsgTrace(boolean) : public throws (void)
Method setEnableStreamRequestType(boolean) : public throws (void)
Method setHeartbeatBrokerInterval(int) : public throws (void)
Method setInstanceName(java.lang.String) : public throws (void)
Method setLanguage(org.apache.rocketmq.remoting.protocol.LanguageCode) : public throws (void)
Method setMessageModel(org.apache.rocketmq.remoting.protocol.heartbeat.MessageModel) : public throws (void)
Method setMessageQueueListener(org.apache.rocketmq.client.consumer.MessageQueueListener) : public throws (void)
Method setMqClientApiTimeout(int) : public throws (void)
Method setNamespace(java.lang.String) : public throws (void)
Method setNamesrvAddr(java.lang.String) : public throws (void)
Method setOffsetStore(org.apache.rocketmq.client.consumer.store.OffsetStore) : public throws (void)
Method setPersistConsumerOffsetInterval(int) : public throws (void)
Method setPollNameServerInterval(int) : public throws (void)
Method setPollTimeoutMillis(long) : public throws (void)
Method setPullBatchSize(int) : public throws (void)
Method setPullThreadNums(int) : public throws (void)
Method setPullThresholdForAll(long) : public throws (void)
Method setPullThresholdForQueue(int) : public throws (void)
Method setPullThresholdSizeForQueue(int) : public throws (void)
Method setPullTimeDelayMillsWhenException(long) : public throws (void)
Method setTopicMetadataCheckIntervalMillis(long) : public throws (void)
Method setUnitMode(boolean) : public throws (void)
Method setUnitName(java.lang.String) : public throws (void)
Method setUseTLS(boolean) : public throws (void)
Method setVipChannelEnabled(boolean) : public throws (void)
Method shutdown() : public throws (void)
Method start() : public throws (void)
Method subscribe(java.lang.String,java.lang.String) : public throws (void)
Method subscribe(java.lang.String,org.apache.rocketmq.client.consumer.MessageSelector) : public throws (void)
Method toString() : public throws (java.lang.String)
Method unsubscribe(java.lang.String) : public throws (void)
Method updateNameServerAddress(java.lang.String) : public throws (void)
Method withNamespace(java.lang.String) : public throws (java.lang.String)
Method withNamespace(java.util.Set) : public throws (java.util.Set)
Method withoutNamespace(java.lang.String) : public throws (java.lang.String)
Method withoutNamespace(java.util.Set) : public throws (java.util.Set)
