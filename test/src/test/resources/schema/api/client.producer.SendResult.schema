/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field messageQueue : private org.apache.rocketmq.common.message.MessageQueue null
Field msgId : private java.lang.String null
Field offsetMsgId : private java.lang.String null
Field queueOffset : private long 0
Field regionId : private java.lang.String null
Field sendStatus : private org.apache.rocketmq.client.producer.SendStatus null
Field traceOn : private boolean true
Field transactionId : private java.lang.String null
Method decoderSendResultFromJson(java.lang.String) : public throws (org.apache.rocketmq.client.producer.SendResult)
Method encoderSendResultToJson(java.lang.Object) : public throws (java.lang.String)
Method getMessageQueue() : public throws (org.apache.rocketmq.common.message.MessageQueue)
Method getMsgId() : public throws (java.lang.String)
Method getOffsetMsgId() : public throws (java.lang.String)
Method getQueueOffset() : public throws (long)
Method getRegionId() : public throws (java.lang.String)
Method getSendStatus() : public throws (org.apache.rocketmq.client.producer.SendStatus)
Method getTransactionId() : public throws (java.lang.String)
Method isTraceOn() : public throws (boolean)
Method setMessageQueue(org.apache.rocketmq.common.message.MessageQueue) : public throws (void)
Method setMsgId(java.lang.String) : public throws (void)
Method setOffsetMsgId(java.lang.String) : public throws (void)
Method setQueueOffset(long) : public throws (void)
Method setRegionId(java.lang.String) : public throws (void)
Method setSendStatus(org.apache.rocketmq.client.producer.SendStatus) : public throws (void)
Method setTraceOn(boolean) : public throws (void)
Method setTransactionId(java.lang.String) : public throws (void)
Method toString() : public throws (java.lang.String)
