/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.rocketmq.controller.impl.event;

import java.util.HashSet;
import java.util.Set;

/**
 * The event alters the syncStateSet of target broker.
 * Triggered by the AlterSyncStateSetApi.
 */
public class AlterSyncStateSetEvent implements EventMessage {

    private final String brokerName;
    private final Set<Long/*BrokerId*/> newSyncStateSet;

    public AlterSyncStateSetEvent(String brokerName, Set<Long> newSyncStateSet) {
        this.brokerName = brokerName;
        this.newSyncStateSet = new HashSet<>(newSyncStateSet);
    }

    @Override
    public EventType getEventType() {
        return EventType.ALTER_SYNC_STATE_SET_EVENT;
    }

    public String getBrokerName() {
        return brokerName;
    }

    public Set<Long> getNewSyncStateSet() {
        return new HashSet<>(newSyncStateSet);
    }

    @Override
    public String toString() {
        return "AlterSyncStateSetEvent{" +
            "brokerName='" + brokerName + '\'' +
            ", newSyncStateSet=" + newSyncStateSet +
            '}';
    }
}
