Apache RocketMQ Developer Guide
--------

##### This guide helps developers to understand and use Apache RocketMQ quickly.

### 1. Concepts & Features

- [Concept](Concept.md): introduce basic concepts in RocketMQ.

- [Feature](Feature.md): introduce functional features of RocketMQ's implementations.


### 2. Architecture Design

- [Architecture](architecture.md): introduce RocketMQ's deployment and technical architecture.

- [Design](design.md): introduce design concept of RocketMQ's key mechanisms, including message storage, communication mechanisms, message filter, loadbalance, transaction message, etc.


### 3. Example

- [Example](RocketMQ_Example.md): introduce RocketMQ's common usage, including basic example, sequence message example, delay message example, batch message example, filter message example, transaction message example, etc.


### 4. Best Practice
- [Best Practice](best_practice.md): introduce RocketMQ's best practice, including producer, consumer, broker, NameServer, configuration of client, and the best parameter configuration of JVM, linux.

- [Message Trace](msg_trace/user_guide.md): introduce how to use RocketMQ's message tracing feature.

- [Auth Management](acl/Operations_ACL.md): introduce how to deployment quickly and how to use RocketMQ cluster enabling auth management feature.

- [Quick Start](dledger/quick_start.md): introduce how to deploy Dledger quickly.

- [Cluster Deployment](dledger/deploy_guide.md): introduce how to deploy Dledger in cluster.

- [Proxy Deployment](proxy/deploy_guide.md)
  Introduce how to deploy proxy (both `Local` mode and `Cluster` mode).

### 5. Operation and maintenance management
- [Operation](operation.md): introduce RocketMQ's deployment modes that including single-master mode, multi-master mode, multi-master multi-slave mode and so on, as well as the usage of operation tool mqadmin.


### 6. API Reference（TODO）

- [DefaultMQProducer API Reference](client/java/API_Reference_DefaultMQProducer.md)







