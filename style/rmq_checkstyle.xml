<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<!DOCTYPE module PUBLIC
    "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
    "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">
<!--Refer http://checkstyle.sourceforge.net/reports/google-java-style.html#s2.2-file-encoding -->
<module name="Checker">

    <property name="localeLanguage" value="en"/>

    <!--To configure the check to report on the first instance in each file-->
    <module name="FileTabCharacter"/>

    <!-- header -->
    <module name="RegexpHeader">
        <property name="header" value="/\*\nLicensed to the Apache Software Foundation*"/>
        <property name="fileExtensions" value="java"/>
    </module>

    <module name="RegexpHeader">
        <property name="header" value="#[\s]*Licensed to the Apache Software Foundation*"/>
        <property name="fileExtensions" value="properties"/>
    </module>

    <module name="RegexpSingleline">
        <property name="format" value="System\.out\.println"/>
        <property name="message" value="Prohibit invoking System.out.println in source code !"/>
    </module>

    <module name="RegexpSingleline">
        <property name="format" value="//FIXME"/>
        <property name="message" value="Recommended fix FIXME task !"/>
    </module>

    <module name="RegexpSingleline">
        <property name="format" value="//TODO"/>
        <property name="message" value="Recommended fix TODO task !"/>
    </module>

    <module name="RegexpSingleline">
        <property name="format" value="@alibaba"/>
        <property name="message" value="Recommended remove @alibaba keyword!"/>
    </module>
    <module name="RegexpSingleline">
        <property name="format" value="@taobao"/>
        <property name="message" value="Recommended remove @taobao keyword!"/>
    </module>
    <module name="RegexpSingleline">
        <property name="format" value="@author"/>
        <property name="message" value="Recommended remove <AUTHOR> in javadoc!"/>
    </module>

    <module name="RegexpSingleline">
        <property name="format"
                  value=".*[\u3400-\u4DB5\u4E00-\u9FA5\u9FA6-\u9FBB\uF900-\uFA2D\uFA30-\uFA6A\uFA70-\uFAD9\uFF00-\uFFEF\u2E80-\u2EFF\u3000-\u303F\u31C0-\u31EF]+.*"/>
        <property name="message" value="Not allow chinese character !"/>
    </module>

    <module name="FileLength">
        <property name="max" value="5000"/>
    </module>

    <module name="TreeWalker">

        <module name="UnusedImports">
            <property name="processJavadoc" value="true"/>
        </module>
        <module name="RedundantImport"/>

        <!--<module name="IllegalImport" />-->

        <!--Checks that classes that override equals() also override hashCode()-->
        <module name="EqualsHashCode"/>
        <!--Checks for over-complicated boolean expressions. Currently finds code like if (topic == true), topic || true, !false, etc.-->
        <module name="SimplifyBooleanExpression"/>
        <module name="OneStatementPerLine"/>
        <module name="UnnecessaryParentheses"/>
        <!--Checks for over-complicated boolean return statements. For example the following code-->
        <module name="SimplifyBooleanReturn"/>

        <!--Check that the default is after all the cases in producerGroup switch statement-->
        <module name="DefaultComesLast"/>
        <!--Detects empty statements (standalone ";" semicolon)-->
        <module name="EmptyStatement"/>
        <!--Checks that long constants are defined with an upper ell-->
        <module name="UpperEll"/>
        <module name="ConstantName">
            <property name="format" value="(^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$)|(^log[a-zA-Z0-9]*$)"/>
        </module>
        <!--Checks that local, non-final variable names conform to producerGroup format specified by the format property-->
        <module name="LocalVariableName"/>
        <!--Validates identifiers for local, final variables, including catch parameters-->
        <module name="LocalFinalVariableName"/>
        <!--Validates identifiers for non-static fields-->
        <module name="MemberName"/>
        <!--Validates identifiers for class type parameters-->
        <module name="ClassTypeParameterName">
            <property name="format" value="^[A-Z0-9]*$"/>
        </module>
        <!--Validates identifiers for method type parameters-->
        <module name="MethodTypeParameterName">
            <property name="format" value="^[A-Z0-9]*$"/>
        </module>
        <module name="PackageName"/>
        <module name="ParameterName"/>
        <module name="StaticVariableName"/>
        <module name="TypeName"/>
        <!--Checks that there are no import statements that use the * notation-->
        <module name="AvoidStarImport"/>

        <!--whitespace-->
        <module name="GenericWhitespace"/>
        <!--<module name="NoWhitespaceBefore"/>-->
        <!--<module name="NoWhitespaceAfter"/>-->
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="true"/>
            <property name="allowEmptyMethods" value="true"/>
        </module>
        <module name="Indentation"/>
        <module name="MethodParamPad"/>
        <module name="ParenPad"/>
        <module name="TypecastParenPad"/>
    </module>
</module>
